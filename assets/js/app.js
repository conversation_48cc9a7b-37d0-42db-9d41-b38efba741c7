// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//
// If you have dependencies that try to import CSS, esbuild will generate a separate `app.css` file.
// To load it, simply add a second `<link>` to your `root.html.heex` file.

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"
import Sortable from "sortablejs"
import autoAnimate from '@formkit/auto-animate'
import { gsap } from "gsap"

const csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// Define hooks
const Hooks = {


  BrokerTabsSortable: {
    mounted() {
      const container = this.el;

      // Initialize Sortable for broker tabs
      this.sortable = new Sortable(container, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',

        // Only allow dragging of broker tabs, not buttons or close buttons
        filter: 'button, .add-broker-btn',
        preventOnFilter: true,

        // Prevent dragging when clicking on close buttons
        onStart: (evt) => {
          // Check if the click target is a close button or its child
          const target = evt.originalEvent.target;
          const closeButton = target.closest('span[phx-click="close_broker_tab"]');
          if (closeButton) {
            // Cancel the drag operation
            return false;
          }
        },

        // Event triggered when sorting is stopped
        onEnd: (evt) => {
          const oldIndex = evt.oldIndex;
          const newIndex = evt.newIndex;

          // Only push the event if the order actually changed
          if (oldIndex !== newIndex) {
            // Send the reordering event to the server
            this.pushEvent('reorder_broker_tabs', {
              old_index: oldIndex,
              new_index: newIndex
            });
          }
        }
      });

      // Handle close button clicks manually to ensure they work
      this.handleCloseButtonClick = (event) => {
        const closeButton = event.target.closest('span[phx-click="close_broker_tab"]');
        if (closeButton) {
          event.stopPropagation();
          event.preventDefault();

          // Get the broker name from the phx-value-name attribute
          const brokerName = closeButton.getAttribute('phx-value-name');
          if (brokerName) {
            // Send the close event to Phoenix LiveView
            this.pushEvent('close_broker_tab', { name: brokerName });
          }
        }
      };

      // Add click event listener to the container for close buttons
      container.addEventListener('click', this.handleCloseButtonClick);
    },

    destroyed() {
      // Clean up event listeners
      if (this.handleCloseButtonClick) {
        this.el.removeEventListener('click', this.handleCloseButtonClick);
      }

      // Clean up Sortable instance when the element is removed
      if (this.sortable) {
        this.sortable.destroy();
      }
    }
  },

  // Hook for handling protocol changes in connection set forms
  ProtocolSelector: {
    mounted() {
      this.handleProtocolChange = () => {
        const protocol = this.el.value;
        const sslContainer = document.getElementById('ssl-section-container');
        const portInput = document.querySelector('input[name="connection_set[port]"]');

        // Show SSL section for secure protocols
        if (sslContainer) {
          if (['mqtts', 'wss', 'quic'].includes(protocol)) {
            sslContainer.style.display = 'block';
          } else {
            sslContainer.style.display = 'none';
          }
        }

        // Update port field based on protocol
        if (portInput) {
          // Only update port if it's a default port value
          const defaultPorts = {
            mqtt: "1883",
            mqtts: "8883",
            ws: "8083",
            wss: "8084",
            quic: "14567"
          };

          // Check if current port is one of the default ports
          const isDefaultPort = Object.values(defaultPorts).includes(portInput.value);

          // Only update if it's a default port to avoid overwriting custom ports
          if (isDefaultPort) {
            portInput.value = defaultPorts[protocol] || "1883";
          }
        }
      };

      // Set initial state
      this.handleProtocolChange();

      // Add event listener for changes
      this.el.addEventListener('change', this.handleProtocolChange);
    },

    destroyed() {
      // Clean up event listener
      if (this.handleProtocolChange) {
        this.el.removeEventListener('change', this.handleProtocolChange);
      }
    }
  },

  // We're now using the onchange attribute directly on the radio buttons
  // instead of a hook for certificate type selection

  // We've removed the ConnectionStatusSortable and ConnectionStatusContainer hooks
  // as we've replaced the component with a table view

  // Hook for handling connection table animations with GSAP
  ConnectionTableAutoAnimate: {
    mounted() {
      this.setupGSAPAnimations();
      this.observeTableChanges();
    },

    updated() {
      // Re-observe table changes when component updates
      this.observeTableChanges();
    },

    setupGSAPAnimations() {
      // Set up GSAP defaults for smooth animations
      gsap.defaults({
        duration: 0.4,
        ease: "power2.out"
      });

      // Store reference to table body for animations
      this.tableBody = this.el.querySelector('tbody');
      this.previousRows = new Map();

      if (this.tableBody) {
        // Store initial state of rows
        this.captureRowState();
      }
    },

    observeTableChanges() {
      if (!this.tableBody) return;

      // Create a mutation observer to detect row changes
      if (this.observer) {
        this.observer.disconnect();
      }

      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            this.handleRowChanges(mutation);
          }
        });
      });

      this.observer.observe(this.tableBody, {
        childList: true,
        subtree: true
      });

      // Also observe status changes within existing rows
      if (this.statusObserver) {
        this.statusObserver.disconnect();
      }

      this.statusObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            this.handleStatusChange(mutation.target);
          }
        });
      });

      // Observe all status elements for changes
      const statusElements = this.tableBody.querySelectorAll('.swap input[type="checkbox"]');
      statusElements.forEach(element => {
        this.statusObserver.observe(element, {
          attributes: true,
          attributeFilter: ['checked']
        });
      });
    },

    captureRowState() {
      if (!this.tableBody) return;

      const rows = this.tableBody.querySelectorAll('tr');
      this.previousRows.clear();

      rows.forEach((row, index) => {
        const clientId = this.getRowClientId(row);
        if (clientId) {
          this.previousRows.set(clientId, {
            element: row,
            index: index,
            rect: row.getBoundingClientRect()
          });
        }
      });
    },

    getRowClientId(row) {
      // Extract client ID from the row's data or content
      const clientIdCell = row.querySelector('td:nth-child(2) span');
      return clientIdCell ? clientIdCell.textContent.trim() : null;
    },

    handleRowChanges(mutation) {
      // Handle removed nodes (connections disappearing)
      mutation.removedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowRemoval(node);
        }
      });

      // Handle added nodes (connections appearing)
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
          this.animateRowAddition(node);
        }
      });

      // Update row state after changes
      setTimeout(() => this.captureRowState(), 50);
    },

    handleStatusChange(element) {
      // Find the row containing this status element
      const row = element.closest('tr');
      if (!row) return;

      // Add a visual indicator that the status is changing
      row.classList.add('status-changing');

      // Add a subtle pulse animation to indicate the change
      gsap.fromTo(row,
        {
          backgroundColor: 'rgba(59, 130, 246, 0.15)',
          scale: 1
        },
        {
          duration: 0.6,
          backgroundColor: 'transparent',
          scale: 1.01,
          ease: "power2.out",
          yoyo: true,
          repeat: 1,
          onComplete: () => {
            row.classList.remove('status-changing');
          }
        }
      );

      // The row removal will be handled by the main mutation observer
      // when LiveView updates the DOM
    },

    animateRowRemoval(row) {
      // Create a clone of the row for animation
      const clone = row.cloneNode(true);
      clone.style.position = 'absolute';
      clone.style.left = '0';
      clone.style.right = '0';
      clone.style.zIndex = '10';
      clone.style.pointerEvents = 'none';

      // Insert clone at the same position
      if (row.parentNode) {
        row.parentNode.insertBefore(clone, row.nextSibling);
      }

      // Animate the clone out
      gsap.to(clone, {
        duration: 0.5,
        x: -50,
        opacity: 0,
        scale: 0.95,
        ease: "power2.in",
        onComplete: () => {
          if (clone.parentNode) {
            clone.parentNode.removeChild(clone);
          }
        }
      });

      // Animate remaining rows moving up
      this.animateRemainingRows();
    },

    animateRowAddition(row) {
      // Set initial state for new row
      gsap.set(row, {
        x: 30,
        opacity: 0,
        scale: 0.98
      });

      // Animate row in
      gsap.to(row, {
        duration: 0.4,
        x: 0,
        opacity: 1,
        scale: 1,
        ease: "back.out(1.2)",
        delay: 0.1
      });
    },

    animateRemainingRows() {
      if (!this.tableBody) return;

      const currentRows = this.tableBody.querySelectorAll('tr');

      currentRows.forEach((row, index) => {
        // Add subtle animation to show the table is updating
        gsap.fromTo(row,
          {
            y: -5,
            opacity: 0.8
          },
          {
            duration: 0.3,
            y: 0,
            opacity: 1,
            ease: "power2.out",
            delay: index * 0.02 // Stagger effect
          }
        );
      });
    },

    destroyed() {
      // Clean up observers
      if (this.observer) {
        this.observer.disconnect();
      }

      if (this.statusObserver) {
        this.statusObserver.disconnect();
      }

      // Kill any running GSAP animations
      if (this.tableBody) {
        gsap.killTweensOf(this.tableBody.querySelectorAll('tr'));
      }
    }
  },



  // Hook for handling trace table smart scrolling
  TraceTableSmartScroll: {
    mounted() {
      this.setupSmartScroll();
      this.setupAutoAnimate();

      // Initial scroll to bottom when component is first mounted
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    },

    updated() {
      // Use setTimeout to ensure DOM has been updated
      setTimeout(() => {
        this.checkScrollPosition();
        // If user was at bottom and new rows were added, scroll to bottom
        if (this.shouldScrollToBottom) {
          this.scrollToBottom();
          this.shouldScrollToBottom = false;
        }
      }, 10);
    },

    setupSmartScroll() {
      const tableBody = this.el;
      if (!tableBody) return;

      // Find the scrollable container (usually the parent with overflow)
      this.scrollContainer = this.findScrollContainer(tableBody);
      this.tableBody = tableBody;
      this.isUserAtBottom = true; // Start assuming user is at bottom
      this.scrollThreshold = 30; // Reduced threshold for more accurate detection
      this.isInitialized = false; // Track if we've done initial setup

      // Track scroll position
      this.handleScroll = () => {
        this.checkScrollPosition();
      };

      if (this.scrollContainer) {
        this.scrollContainer.addEventListener('scroll', this.handleScroll, { passive: true });
      }

      // Store initial row count
      this.previousRowCount = tableBody.children.length;
      this.shouldScrollToBottom = false;
    },

    setupAutoAnimate() {
      const tableBody = this.el;
      if (!tableBody) return;

      // Apply autoAnimate with smart scroll behavior
      autoAnimate(tableBody, {
        duration: 300,
        easing: 'ease-out',
        disrespectUserMotionPreference: false,
        animation: (element, action, oldCoords, newCoords) => {
          let keyframes = [];
          let options = { duration: 300, easing: 'ease-out' };

          if (action === 'add') {
            // New message appearing - only animate if user is at bottom
            if (this.isUserAtBottom) {
              keyframes = [
                {
                  transform: 'translateY(20px)',
                  opacity: 0,
                  backgroundColor: 'rgba(34, 197, 94, 0.15)',
                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.3)'
                },
                {
                  transform: 'translateY(10px)',
                  opacity: 0.7,
                  backgroundColor: 'rgba(34, 197, 94, 0.08)',
                  boxShadow: '0 1px 4px rgba(34, 197, 94, 0.2)'
                },
                {
                  transform: 'translateY(0)',
                  opacity: 1,
                  backgroundColor: 'transparent',
                  boxShadow: 'none'
                }
              ];
              options.duration = 400;
            } else {
              // User is not at bottom - minimal animation
              keyframes = [
                { opacity: 0 },
                { opacity: 1 }
              ];
              options.duration = 150;
            }
          } else if (action === 'remove') {
            keyframes = [
              { opacity: 1, backgroundColor: 'transparent' },
              { opacity: 0, backgroundColor: 'rgba(239, 68, 68, 0.05)' }
            ];
            options.duration = 200;
          } else if (action === 'remain') {
            // Existing messages - only animate if user is at bottom
            if (this.isUserAtBottom) {
              const deltaY = (oldCoords?.top || 0) - (newCoords?.top || 0);
              if (Math.abs(deltaY) > 2) {
                keyframes = [
                  { transform: `translateY(${Math.min(deltaY, 15)}px)` },
                  { transform: 'translateY(0)' }
                ];
                options.duration = 250;
              }
            }
          }

          if (keyframes.length > 0) {
            return element.animate(keyframes, options);
          }
        }
      });
    },

    findScrollContainer(element) {
      let parent = element.parentElement;
      while (parent && parent !== document.body) {
        const style = window.getComputedStyle(parent);
        // Check for the specific trace table wrapper class first
        if (parent.classList.contains('trace-message-table-wrapper')) {
          console.log('Found trace table wrapper as scroll container');
          return parent;
        }
        // Then check for overflow styles
        if (style.overflow === 'auto' || style.overflow === 'scroll' ||
            style.overflowY === 'auto' || style.overflowY === 'scroll') {
          console.log('Found scroll container by overflow style:', parent);
          return parent;
        }
        parent = parent.parentElement;
      }
      console.log('Using window as fallback scroll container');
      return window; // Fallback to window
    },

    checkScrollPosition() {
      if (!this.scrollContainer || !this.tableBody) return;

      let scrollTop, scrollHeight, clientHeight;

      if (this.scrollContainer === window) {
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        scrollTop = this.scrollContainer.scrollTop;
        scrollHeight = this.scrollContainer.scrollHeight;
        clientHeight = this.scrollContainer.clientHeight;
      }

      // Check if user is near the bottom
      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
      const wasAtBottom = this.isUserAtBottom;
      this.isUserAtBottom = distanceFromBottom <= this.scrollThreshold;

      // If new rows were added and user was at bottom, mark for scrolling
      const currentRowCount = this.tableBody.children.length;
      if (currentRowCount > this.previousRowCount && wasAtBottom) {
        this.shouldScrollToBottom = true;
      }
      this.previousRowCount = currentRowCount;

      // Debug logging (can be removed in production)
      console.log('Scroll check:', {
        distanceFromBottom,
        isUserAtBottom: this.isUserAtBottom,
        shouldScrollToBottom: this.shouldScrollToBottom,
        currentRowCount,
        previousRowCount: this.previousRowCount
      });
    },

    scrollToBottom() {
      if (!this.scrollContainer) return;

      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        if (this.scrollContainer === window) {
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth'
          });
        } else {
          const scrollHeight = this.scrollContainer.scrollHeight;
          console.log('Scrolling to bottom:', {
            scrollHeight,
            currentScrollTop: this.scrollContainer.scrollTop
          });
          this.scrollContainer.scrollTo({
            top: scrollHeight,
            behavior: 'smooth'
          });
        }
      });
    },

    destroyed() {
      if (this.scrollContainer && this.handleScroll) {
        this.scrollContainer.removeEventListener('scroll', this.handleScroll);
      }
    }
  },

  // Hook for handling trace row highlighting
  TraceRowHighlight: {
    mounted() {
      this.setupRowHighlighting();
    },

    updated() {
      this.setupRowHighlighting();
    },

    setupRowHighlighting() {
      const container = this.el;
      const tableBody = container.querySelector('#trace-message-table-body');
      if (!tableBody) return;

      // Remove existing event listeners to avoid duplicates
      if (this.handleRowClick) {
        tableBody.removeEventListener('click', this.handleRowClick);
      }

      // Add click event listener to the table body
      this.handleRowClick = (event) => {
        const clickedRow = event.target.closest('tr');
        if (!clickedRow || clickedRow.id === 'loading-more-row') return;

        // Remove highlight from all rows
        const allRows = tableBody.querySelectorAll('tr.trace-message-row');
        allRows.forEach(row => {
          row.classList.remove('js-highlighted');
        });

        // Add highlight to clicked row with animation
        clickedRow.classList.add('js-highlighted');

        // Add a subtle pulse effect
        clickedRow.style.animation = 'none';
        // Force reflow
        clickedRow.offsetHeight;
        clickedRow.style.animation = 'row-highlight-pulse 0.4s ease-out';

        // Remove the animation after it completes
        setTimeout(() => {
          if (clickedRow.style) {
            clickedRow.style.animation = '';
          }
        }, 400);
      };

      tableBody.addEventListener('click', this.handleRowClick);
      // Store reference for cleanup
      this.tableBody = tableBody;
    },

    destroyed() {
      if (this.tableBody && this.handleRowClick) {
        this.tableBody.removeEventListener('click', this.handleRowClick);
      }
    }
  }
};

const liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken},
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// Add custom event handlers
window.addEventListener("phx:focus_element", (e) => {
  const element = document.getElementById(e.detail.id);
  if (element) {
    setTimeout(() => {
      element.focus();
    }, 50);
  }
});

// Update certificate files visibility when modal is shown
window.addEventListener("phx:show", (_e) => {
  // Wait a bit for the DOM to be updated
  setTimeout(() => {
    window.updateCertificateFilesVisibility();
  }, 100);
});



// Initialize functionality
document.addEventListener("DOMContentLoaded", () => {
  // Initialize certificate files visibility
  window.updateCertificateFilesVisibility();
});



// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// Function to update certificate files visibility based on selected certificate type
window.updateCertificateFilesVisibility = function() {
  const certificateFilesSection = document.getElementById('certificate-files-section');
  if (!certificateFilesSection) return;

  const selfSignedRadio = document.getElementById('certificate-type-self-signed');
  if (selfSignedRadio && selfSignedRadio.checked) {
    certificateFilesSection.style.display = 'block';
  } else {
    certificateFilesSection.style.display = 'none';
  }
}

// The lines below enable quality of life phoenix_live_reload
// development features:
//
//     1. stream server logs to the browser console
//     2. click on elements to jump to their definitions in your code editor
//
if (process.env.NODE_ENV === "development") {
  window.addEventListener("phx:live_reload:attached", ({detail: reloader}) => {
    // Enable server log streaming to client.
    // Disable with reloader.disableServerLogs()
    reloader.enableServerLogs()

    // Open configured PLUG_EDITOR at file:line of the clicked element's HEEx component
    //
    //   * click with "c" key pressed to open at caller location
    //   * click with "d" key pressed to open at function component definition location
    let keyDown
    window.addEventListener("keydown", e => keyDown = e.key)
    window.addEventListener("keyup", _e => keyDown = null)
    window.addEventListener("click", e => {
      if(keyDown === "c"){
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtCaller(e.target)
      } else if(keyDown === "d"){
        e.preventDefault()
        e.stopImmediatePropagation()
        reloader.openEditorAtDef(e.target)
      }
    }, true)

    window.liveReloader = reloader
  })
}

