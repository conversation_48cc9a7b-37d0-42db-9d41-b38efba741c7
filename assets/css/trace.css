/* Trace component specific styles */

/* Red-themed background for trace section */
.trace-section {
  background-color: rgba(254, 226, 226, 0.7); /* Light red background with transparency */
  border: 1px solid rgba(239, 68, 68, 0.3); /* Red border with transparency */
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Trace section header */
.trace-section-header {
  color: rgb(185, 28, 28); /* Dark red text */
  font-weight: 600;
}

/* Trace message table container - fixed height for exactly 15 rows */
.trace-message-table-container {
  background-color: white;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  /* Fixed height calculation: header (2.5rem) + 15 rows (1.75rem each) + padding */
  height: calc(2.5rem + 15 * 1.75rem + 0.5rem);
  max-height: calc(2.5rem + 15 * 1.75rem + 0.5rem);
  display: flex;
  flex-direction: column;
}

/* Table wrapper with scrolling */
.trace-message-table-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* Ensure smooth scrolling */
  scroll-behavior: smooth;
}

/* Table styling */
.trace-message-table-wrapper table {
  min-width: 100%;
  table-layout: fixed;
}

/* Ensure table headers stay visible during scroll */
.trace-message-table-wrapper thead th {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
  height: 2.5rem;
}

/* Table row height consistency */
.trace-message-table-wrapper tbody tr {
  height: 1.75rem;
  min-height: 1.75rem;
  max-height: 1.75rem;
}

/* Selected message row */
.trace-message-selected {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-left: 3px solid rgb(239, 68, 68) !important;
}

/* Message type badges */
.badge-publish {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
  border-color: rgb(59, 130, 246);
}

.badge-subscribe {
  background-color: rgba(139, 92, 246, 0.1);
  color: rgb(139, 92, 246);
  border-color: rgb(139, 92, 246);
}

.badge-connect {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
  border-color: rgb(16, 185, 129);
}

/* Message details panel */
.trace-details-panel {
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Payload display area */
.trace-payload-display {
  font-family: monospace;
  background-color: rgba(243, 244, 246, 0.7);
  border-radius: 0.25rem;
  padding: 0.5rem;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

/* Message direction visual indicators - subtle and stable */
.trace-message-row {
  transition: background-color 0.2s ease-out, box-shadow 0.2s ease-out;
}

/* Direction indicators with meaningful colors */
.trace-message-in {
  border-left: 3px solid rgba(34, 197, 94, 0.4);
}

.trace-message-out {
  border-left: 3px solid rgba(234, 179, 8, 0.4);
}

.trace-message-neutral {
  border-left: 3px solid rgba(156, 163, 175, 0.4);
}

/* Only animate on hover for existing rows - very subtle */
.trace-message-row:hover {
  background-color: rgba(59, 130, 246, 0.03) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}



/* JavaScript-controlled row highlighting - more prominent */
.trace-message-row.js-highlighted {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border-left: 5px solid rgb(59, 130, 246) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
  transform: translateX(3px);
  transition: all 0.2s ease-out;
}

/* Ensure JS highlight takes precedence over server-side selection */
.trace-message-row.js-highlighted.trace-message-selected {
  background-color: rgba(59, 130, 246, 0.18) !important;
  border-left-color: rgb(59, 130, 246) !important;
}

/* Enhanced pulse animation for clicked rows */
@keyframes row-highlight-pulse {
  0% {
    background-color: rgba(59, 130, 246, 0.25);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.35);
    transform: translateX(4px);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.2);
    box-shadow: 0 5px 18px rgba(59, 130, 246, 0.3);
    transform: translateX(3.5px);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.15);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
    transform: translateX(3px);
  }
}

/* Remove the duplicate badge animations - they're handled below */

/* Table cell truncation styles - adjusted for better ClientID visibility */
.table-cell-truncate {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.payload-preview-truncate {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ClientID specific styling */
.trace-client-id {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Timestamp specific styling - fixed width for exactly 18 characters */
.trace-timestamp {
  font-family: monospace;
  width: 144px; /* 18 characters * 8px per character in monospace */
  min-width: 144px;
  max-width: 144px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: clip; /* Don't use ellipsis since we control the length */
}

/* Loading row animation */
.trace-loading-row {
  animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .trace-message-table-container {
    /* Adjust height for mobile - fewer visible rows */
    height: calc(2.5rem + 10 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 10 * 1.75rem + 0.5rem);
  }

  .table-cell-truncate {
    max-width: 120px;
  }

  .payload-preview-truncate {
    max-width: 150px;
  }

  .trace-client-id {
    max-width: 140px;
  }
}

@media (max-width: 480px) {
  .trace-message-table-container {
    /* Further reduce for very small screens */
    height: calc(2.5rem + 8 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 8 * 1.75rem + 0.5rem);
  }

  .table-cell-truncate {
    max-width: 100px;
  }

  .payload-preview-truncate {
    max-width: 120px;
  }

  .trace-client-id {
    max-width: 110px;
  }
}

/* Smooth scrollbar styling */
.trace-message-table-wrapper::-webkit-scrollbar {
  width: 6px;
}

.trace-message-table-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.trace-message-table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.trace-message-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Ensure smooth transitions for interactive elements - very subtle */
.trace-direction,
.trace-message-type,
.trace-client-id {
  transition: transform 0.15s ease-out;
}

/* Subtle hover effects for badges only */
.trace-direction:hover,
.trace-message-type:hover {
  transform: scale(1.02);
}

/* Smooth scroll behavior for the table container */
.trace-message-table-wrapper {
  scroll-behavior: smooth;
}
