defmodule MqttableWeb.ConnectionsLive do
  use MqttableWeb, :live_view
  require Logger

  alias Mqttable.ConnectionSets
  alias Mqttable.MqttClient.Manager, as: MqttClientManager
  alias MqttableWeb.ConnectionSets.Manager, as: ConnectionSetsManager
  alias MqttableWeb.Connections.Manager, as: ConnectionsManager
  alias MqttableWeb.Variables.Manager, as: VariablesManager
  alias MqttableWeb.UI.StateManager
  alias MqttableWeb.Utils.ConnectionHelpers

  @impl true
  def mount(_params, _session, socket) do
    # Get connection sets from the ConnectionSets server
    connection_sets = ConnectionSets.get_all()

    # Subscribe to connection sets updates, MQTT client status updates, and MQTT trace messages
    if connected?(socket) do
      ConnectionSets.subscribe()
      Phoenix.PubSub.subscribe(Mqttable.PubSub, "mqtt_clients")
      Mqttable.EmqttLoggerHandler.subscribe()
    end

    # Initialize subscription modal related assigns
    socket =
      socket
      |> assign(:show_subscription_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:edit_mode, false)
      |> assign(:client_id, nil)
      |> assign(:topic, nil)
      |> assign(:qos, 0)
      |> assign(:nl, false)
      |> assign(:rap, false)
      |> assign(:rh, 0)
      |> assign(:sub_id, nil)
      |> assign(:index, nil)

    available_colors = [
      {"blue", "bg-blue-500"},
      {"green", "bg-green-500"},
      {"red", "bg-red-500"},
      {"yellow", "bg-yellow-500"},
      {"purple", "bg-purple-500"}
    ]

    # Initialize a new connection with default values
    default_connection = %{
      name: ConnectionHelpers.generate_random_connection_name(),
      client_id: "mqttable_" <> ConnectionHelpers.generate_random_string(8),
      username: "",
      password: "",
      mqtt_version: "5.0",
      connect_timeout: 10,
      keep_alive: 300,
      clean_start: true,
      session_expiry_interval: 0,
      receive_maximum: nil,
      maximum_packet_size: nil,
      topic_alias_maximum: nil,
      request_response_info: false,
      request_problem_info: false,
      user_properties: [%{key: "", value: ""}],
      will_topic: "",
      will_qos: "0",
      will_retain: false,
      will_payload: "",
      will_payload_format: false,
      will_delay_interval: 0,
      will_message_expiry: 0,
      will_content_type: "",
      will_response_topic: "",
      will_correlation_data: "",
      topics: [],
      # 默认状态为断开连接
      status: "disconnected",
      # 连接时间，初始为 nil
      connection_time: nil
    }

    # Get UI state from the ConnectionSets server
    ui_state = ConnectionSets.get_ui_state()

    # Get expanded_sets from UI state or initialize as an empty map
    expanded_sets = Map.get(ui_state, :expanded_sets, %{})

    # Get active connection set name from UI state
    active_set_name = Map.get(ui_state, :active_connection_set)

    # Find the active connection set by name
    active_connection_set =
      if active_set_name do
        ConnectionHelpers.find_connection_set_by_name(connection_sets, active_set_name)
      else
        nil
      end

    # Load initial trace messages for the active broker (first page only)
    trace_messages =
      if active_connection_set && active_connection_set.name do
        case Mqttable.TraceManager.get_messages_paginated(active_connection_set.name, 50, 0) do
          {:ok, messages, _has_more} -> messages
          _error -> []
        end
      else
        []
      end

    socket =
      socket
      |> assign(:connection_sets, connection_sets)
      |> assign(:active_connection_set, active_connection_set)
      |> assign(:show_modal, false)
      |> assign(:modal_type, nil)
      |> assign(:edit_var, nil)
      |> assign(:edit_connection_set, nil)
      |> assign(:edit_connection, default_connection)
      |> assign(:available_colors, available_colors)
      |> assign(:uploaded_files, [])
      |> assign(:expanded_sets, expanded_sets)
      # Initialize trace component state with loaded messages
      |> assign(:trace_messages, trace_messages)
      |> assign(:trace_active, false)

    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    # Since we no longer use URL parameters to track the active broker,
    # we just return the socket without making any changes
    {:noreply, socket}
  end

  @impl true
  def handle_event("open_connection_set_modal", %{"type" => "new_connection_set"}, socket) do
    ConnectionSetsManager.handle_open_connection_set_modal(socket)
  end

  @impl true
  def handle_event("open_edit_connection_set_modal", %{"name" => name}, socket) do
    ConnectionSetsManager.handle_open_edit_connection_set_modal(socket, %{"name" => name})
  end

  @impl true
  def handle_event("open_connections_modal", _params, socket) do
    StateManager.handle_open_connections_modal(socket)
  end

  @impl true
  def handle_event("select_broker_tab", %{"name" => name}, socket) do
    # Find the connection set by name
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if set do
      # Mark this change as initiated locally to prevent loops
      socket = assign(socket, :active_set_change_source, "local")

      # Update the active connection set in the UI state to broadcast to all clients
      ConnectionSets.update_active_connection_set(name)

      # Load trace messages for this broker
      trace_messages = Mqttable.TraceManager.get_messages(name)

      {:noreply,
       socket
       |> assign(:active_connection_set, set)
       |> assign(:trace_messages, trace_messages)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_broker_tab", %{"name" => name}, socket) do
    handle_close_broker_tab(socket, name)
  end

  @impl true
  def handle_event(
        "reorder_broker_tabs",
        %{"old_index" => old_index, "new_index" => new_index},
        socket
      ) do
    # Reorder the connection sets based on the new order
    connection_sets = socket.assigns.connection_sets

    # Move the item from old_index to new_index
    {item, remaining} = List.pop_at(connection_sets, old_index)
    reordered_sets = List.insert_at(remaining, new_index, item)

    # Update the connection sets
    ConnectionSets.update(reordered_sets)

    socket = assign(socket, :connection_sets, reordered_sets)
    {:noreply, socket}
  end

  @impl true
  def handle_event("open_new_connection_modal", %{"name" => set_name}, socket) do
    ConnectionsManager.handle_open_new_connection_modal(socket, %{"name" => set_name})
  end

  @impl true
  def handle_event(
        "open_edit_connection_modal",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ConnectionsManager.handle_open_edit_connection_modal(
      socket,
      %{"set_name" => set_name, "client_id" => client_id}
    )
  end

  @impl true
  def handle_event("close_modal", _params, socket) do
    StateManager.handle_close_modal(socket)
  end

  @impl true
  def handle_event("open_subscription_modal", %{"set_name" => set_name}, socket) do
    # Find the connection set by name
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:pre_selected_client_id, nil)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event(
        "open_subscription_modal_for_client",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    # Find the connection set by name
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:pre_selected_client_id, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("edit_subscription", params, socket) do
    client_id = params["client_id"]
    topic = params["topic"]
    qos = params["qos"]
    nl = params["nl"]
    rap = params["rap"]
    rh = params["rh"]
    sub_id = params["sub_id"]
    index = params["index"]
    # Find the connection set by client_id
    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil do
      # Get the connection set
      active_set = Enum.at(connection_sets, set_index)

      # Get the connection and find the topic entry to get the subscription identifier
      connection =
        if connection_index != nil do
          Enum.at(active_set.connections, connection_index)
        else
          nil
        end

      # Find the topic entry using the provided index or by searching for the topic
      {topic_entry, topic_index} =
        if connection && connection.topics do
          topics = connection.topics

          # If index is provided and valid, use it directly
          if index != nil && index != "" do
            case Integer.parse(index) do
              {idx, _} when idx >= 0 and idx < length(topics) ->
                {Enum.at(topics, idx), idx}

              _ ->
                # Invalid index, fall back to finding by topic
                idx =
                  Enum.find_index(topics, fn
                    %{topic: t} -> t == topic
                    _ -> false
                  end)

                if idx != nil do
                  {Enum.at(topics, idx), idx}
                else
                  {nil, nil}
                end
            end
          else
            # No index provided, find by topic
            idx =
              Enum.find_index(topics, fn
                %{topic: t} -> t == topic
                _ -> false
              end)

            if idx != nil do
              {Enum.at(topics, idx), idx}
            else
              {nil, nil}
            end
          end
        else
          {nil, nil}
        end

      # Get subscription identifier from topic entry if available
      sub_id_value =
        cond do
          # If sub_id is provided in params, use it
          sub_id && sub_id != "" ->
            sub_id

          # If topic entry has id field, use it
          topic_entry && is_map(topic_entry) && Map.has_key?(topic_entry, :id) ->
            id = Map.get(topic_entry, :id)
            if is_integer(id) && id > 0, do: Integer.to_string(id), else: ""

          # Otherwise, use empty string
          true ->
            ""
        end

      # Convert string values to appropriate types
      qos_int =
        case qos do
          qos when is_binary(qos) -> String.to_integer(qos)
          qos when is_integer(qos) -> qos
          _ -> 0
        end

      # 保持 nl 和 rap 的原始值，不转换为布尔值
      nl_bool =
        case nl do
          "true" -> 1
          true -> 1
          1 -> 1
          "1" -> 1
          _ -> 0
        end

      rap_bool =
        case rap do
          "true" -> 1
          true -> 1
          1 -> 1
          "1" -> 1
          _ -> 0
        end

      rh_int =
        case rh do
          rh when is_binary(rh) -> String.to_integer(rh)
          rh when is_integer(rh) -> rh
          _ -> 0
        end

      # Open the subscription modal with pre-filled values for editing
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:edit_mode, true)
        |> assign(:client_id, client_id)
        |> assign(:topic, topic)
        |> assign(:qos, qos_int)
        |> assign(:nl, nl_bool)
        |> assign(:rap, rap_bool)
        |> assign(:rh, rh_int)
        |> assign(:sub_id, sub_id_value)
        |> assign(:index, if(topic_index != nil, do: Integer.to_string(topic_index), else: ""))

      {:noreply, socket}
    else
      # Connection set not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("unsubscribe_topic", %{"client_id" => client_id, "topic" => topic}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Attempt to unsubscribe from the topic
      case Mqttable.MqttClient.Manager.unsubscribe(client_id, topic) do
        {:ok, _props, _reason_codes} ->
          # Update the connection's topics list
          current_topics = connection.topics || []

          # Remove the topic from the list (always using new format - list of maps)
          updated_topics =
            Enum.reject(current_topics, fn t ->
              case t do
                %{topic: topic_str} -> topic_str == topic
                _ -> false
              end
            end)

          updated_connection = Map.put(connection, :topics, updated_topics)

          # Update the connection in the connection set
          updated_connections =
            List.replace_at(set.connections, connection_index, updated_connection)

          updated_set = Map.put(set, :connections, updated_connections)

          # Update the connection set in the connection sets list
          updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

          # Update the connection sets in the state
          ConnectionSets.update(updated_connection_sets)

          # Show success message
          socket = put_flash(socket, :info, "Unsubscribed from topic: #{topic}")
          {:noreply, socket}

        {:error, _reason, error_message} ->
          # Show error message
          socket = put_flash(socket, :error, "Failed to unsubscribe: #{error_message}")
          {:noreply, socket}

        {:error, :not_connected} ->
          # Show error message
          socket = put_flash(socket, :error, "Client is not connected")
          {:noreply, socket}
      end
    else
      # Connection not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("generate_client_id", _params, socket) do
    ConnectionsManager.handle_generate_client_id(socket)
  end

  @impl true
  def handle_event("generate_connection_name", _params, socket) do
    ConnectionsManager.handle_generate_connection_name(socket)
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    ConnectionsManager.handle_add_user_property(socket)
  end

  @impl true
  def handle_event("delete_user_property", %{"index" => index}, socket) do
    ConnectionsManager.handle_remove_user_property(socket, %{"index" => index})
  end

  @impl true
  def handle_event("check_new_property", %{"value" => value}, socket) do
    ConnectionsManager.handle_check_new_property(socket, %{"value" => value})
  end

  @impl true
  def handle_event("save_connection", %{"connection" => connection_params}, socket) do
    ConnectionsManager.handle_save_connection(socket, %{"connection" => connection_params})
  end

  @impl true
  def handle_event("save_and_connect_connection", %{"connection" => connection_params}, socket) do
    ConnectionsManager.handle_save_and_connect_connection(socket, %{
      "connection" => connection_params
    })
  end

  @impl true
  def handle_event(
        "update_connection",
        %{"connection" => connection_params, "old_client_id" => old_client_id},
        socket
      ) do
    ConnectionsManager.handle_update_connection(
      socket,
      %{"connection" => connection_params, "old_client_id" => old_client_id}
    )
  end

  @impl true
  def handle_event(
        "delete_connection",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ConnectionsManager.handle_delete_connection(
      socket,
      %{"set_name" => set_name, "client_id" => client_id}
    )
  end

  @impl true
  def handle_event(
        "save_connection_set",
        %{"connection_set" => set_params} = params,
        socket
      ) do
    ConnectionSetsManager.handle_save_connection_set(socket, %{
      "connection_set" => set_params,
      "variable" => Map.get(params, "variable", %{})
    })
  end

  @impl true
  def handle_event(
        "update_connection_set",
        %{"connection_set" => set_params, "old_name" => old_name, "variable" => var_params},
        socket
      ) do
    ConnectionSetsManager.handle_update_connection_set(
      socket,
      %{
        "connection_set" => set_params,
        "old_name" => old_name,
        "variable" => var_params
      }
    )
  end

  @impl true
  def handle_event(
        "update_connection_set",
        %{"connection_set" => set_params, "old_name" => old_name},
        socket
      ) do
    ConnectionSetsManager.handle_update_connection_set(
      socket,
      %{
        "connection_set" => set_params,
        "old_name" => old_name
      }
    )
  end

  @impl true
  def handle_event("save_variable", %{"variable" => var_params}, socket) do
    VariablesManager.handle_save_variable(socket, %{"variable" => var_params})
  end

  @impl true
  def handle_event("update_variable", %{"variable" => var_params, "old_name" => old_name}, socket) do
    VariablesManager.handle_update_variable(socket, %{
      "variable" => var_params,
      "old_name" => old_name
    })
  end

  @impl true
  def handle_event("delete_variable", %{"name" => name}, socket) do
    VariablesManager.handle_delete_variable(socket, %{"name" => name})
  end

  @impl true
  def handle_event("delete_connection_set", %{"name" => name}, socket) do
    ConnectionSetsManager.handle_delete_connection_set(socket, %{"name" => name})
  end

  @impl true
  def handle_event(
        "update_connection_status",
        %{"client_id" => client_id, "new_status" => new_status},
        socket
      ) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Handle MQTT connection based on the new status
        connection_result =
          try do
            case new_status do
              "connected" ->
                # Attempt to establish MQTT connection
                case MqttClientManager.connect(connection, active_set) do
                  {:ok, _} ->
                    {:ok, new_status}

                  {:error, reason, error_message} ->
                    # If connection fails, set status to reconnecting and store the error message
                    Logger.error(
                      "Failed to connect MQTT client #{client_id}: #{inspect(reason)} - #{error_message}"
                    )

                    {:error, "reconnecting", error_message}
                end

              "disconnected" ->
                # Disconnect MQTT client if it was connected
                # Always call disconnect, our improved implementation handles the case
                # when the client is already disconnected
                MqttClientManager.disconnect(client_id)

                # Always return success for disconnection
                {:ok, new_status}

              _ ->
                # For other statuses, just update the status
                {:ok, new_status}
            end
          catch
            :exit, {:shutdown, :tcp_closed} ->
              error_message = "Connection closed by broker before completing handshake"
              Logger.error("MQTT client #{client_id}: #{error_message}")
              {:error, "reconnecting", error_message}

            :exit, {:socket_closed_before_connack, _} ->
              error_message = "Connection closed by broker before completing handshake"
              Logger.error("MQTT client #{client_id}: #{error_message}")
              {:error, "reconnecting", error_message}

            :exit, reason ->
              # Extract just the essential error information without all the parameters
              error_message = extract_concise_error_message(reason)
              Logger.error("MQTT client #{client_id}: #{error_message}")
              {:error, "reconnecting", error_message}
          end

        # Update connection status based on the result
        {status, actual_status, error_message} =
          case connection_result do
            {:ok, status} ->
              {:ok, status, nil}

            {:error, status, message} ->
              {:error, status, message}
          end

        # Update the connections list
        updated_connections =
          Enum.map(connections, fn conn ->
            if conn.client_id == client_id do
              # If the status is changing to connected, add a timestamp
              # If the status is changing to disconnected, clear the connection_time
              cond do
                actual_status == "connected" ->
                  conn
                  |> Map.put(:status, actual_status)
                  |> Map.put(:connection_time, DateTime.utc_now())

                actual_status == "disconnected" ->
                  conn
                  |> Map.put(:status, actual_status)
                  |> Map.put(:connection_time, nil)

                true ->
                  Map.put(conn, :status, actual_status)
              end
            else
              conn
            end
          end)

        # Update the active connection set
        updated_set = Map.put(active_set, :connections, updated_connections)

        # Get all connection sets
        connection_sets = socket.assigns.connection_sets

        # Update the connection sets list
        updated_connection_sets =
          Enum.map(connection_sets, fn set ->
            if set.name == active_set.name do
              updated_set
            else
              set
            end
          end)

        # Update the connection sets in the server
        Mqttable.ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        # Note: No need to manually broadcast, Mqttable.ConnectionSets.update already broadcasts connection_sets_updated event

        # If there was an error, show a notification with the specific error message
        socket =
          if status == :error do
            # Use the specific error message if available
            flash_message =
              if error_message,
                do: error_message,
                else: "Failed to connect to MQTT broker. Retrying..."

            put_flash(socket, :error, flash_message)
          else
            socket
          end

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel-upload", %{"upload" => upload_name, "ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, String.to_existing_atom(upload_name), ref)}
  end

  @impl true
  def handle_event("clear_variables", _params, socket) do
    VariablesManager.handle_clear_variables(socket)
  end

  @impl true
  def handle_event("add_variable_row", params, socket) do
    VariablesManager.handle_add_variable_row(socket, params)
  end

  @impl true
  def handle_event(
        "mqtt_version_changed",
        %{"connection" => %{"mqtt_version" => _mqtt_version}} = params,
        socket
      ) do
    ConnectionsManager.handle_mqtt_version_changed(socket, params)
  end

  @impl true
  def handle_info({:close_broker_tab, name}, socket) do
    # Handle the close broker tab message from the component
    handle_close_broker_tab(socket, name)
  end

  @impl true
  def handle_info({:open_edit_connection_set_modal, params}, socket) do
    # Call the existing handler with the params
    handle_event("open_edit_connection_set_modal", params, socket)
  end

  @impl true
  def handle_info({:focus_element, element_id}, socket) do
    StateManager.handle_focus_element(socket, element_id)
  end

  @impl true
  def handle_info({:connection_sets_updated, updated_connection_sets}, socket) do
    StateManager.handle_connection_sets_updated(socket, updated_connection_sets)
  end

  @impl true
  def handle_info({:ui_state_updated, updated_ui_state}, socket) do
    StateManager.handle_ui_state_updated(socket, updated_ui_state)
  end

  @impl true
  def handle_info({:save_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("save_connection_set", params, socket)
  end

  @impl true
  def handle_info({:update_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("update_connection_set", params, socket)
  end

  @impl true
  def handle_info({:delete_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("delete_connection_set", params, socket)
  end

  @impl true
  def handle_info({:save_variable, params}, socket) do
    # Call the existing handler with the params
    handle_event("save_variable", params, socket)
  end

  @impl true
  def handle_info({:update_variable, params}, socket) do
    # Call the existing handler with the params
    handle_event("update_variable", params, socket)
  end

  @impl true
  def handle_info({:save_connection, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event("save_connection", %{"connection" => connection_params}, socket)
  end

  @impl true
  def handle_info({:save_and_connect_connection, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event("save_and_connect_connection", %{"connection" => connection_params}, socket)
  end

  @impl true
  def handle_info({:update_connection, old_client_id, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event(
      "update_connection",
      %{"connection" => connection_params, "old_client_id" => old_client_id},
      socket
    )
  end

  @impl true
  def handle_info({:mqtt_client_status_changed, client_id, status}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Convert atom status to string
        string_status = Atom.to_string(status)

        # Only update if the status has changed
        if connection.status != string_status do
          # Update the connections list
          updated_connections =
            Enum.map(connections, fn conn ->
              if conn.client_id == client_id do
                # If status is disconnected, clear the connection_time
                if string_status == "disconnected" do
                  conn
                  |> Map.put(:status, string_status)
                  |> Map.put(:connection_time, nil)
                else
                  Map.put(conn, :status, string_status)
                end
              else
                conn
              end
            end)

          # Update the active connection set
          updated_set = Map.put(active_set, :connections, updated_connections)

          # Get all connection sets
          connection_sets = socket.assigns.connection_sets

          # Update the connection sets list
          updated_connection_sets =
            Enum.map(connection_sets, fn set ->
              if set.name == active_set.name do
                updated_set
              else
                set
              end
            end)

          # Update the connection sets in the server
          Mqttable.ConnectionSets.update(updated_connection_sets)

          # Update socket assigns
          socket =
            assign(socket,
              active_connection_set: updated_set,
              connection_sets: updated_connection_sets
            )

          {:noreply, socket}
        else
          {:noreply, socket}
        end
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_connection_error, client_id, error_message}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection that had the error
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Display a concise flash message with the error details
        socket =
          put_flash(socket, :error, "Connection failed for #{client_id}: #{error_message}")

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_connection_time, client_id, timestamp}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Update the connection_time
        updated_connections =
          Enum.map(connections, fn conn ->
            if conn.client_id == client_id do
              Map.put(conn, :connection_time, timestamp)
            else
              conn
            end
          end)

        # Update the active connection set
        updated_set = Map.put(active_set, :connections, updated_connections)

        # Get all connection sets
        connection_sets = socket.assigns.connection_sets

        # Update the connection sets list
        updated_connection_sets =
          Enum.map(connection_sets, fn set ->
            if set.name == active_set.name do
              updated_set
            else
              set
            end
          end)

        # Update the connection sets in the server
        Mqttable.ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:close_subscription_modal}, socket) do
    socket =
      socket
      |> assign(:show_subscription_modal, false)
      |> assign(:edit_mode, false)
      |> assign(:client_id, nil)
      |> assign(:topic, nil)
      |> assign(:qos, 0)
      |> assign(:nl, false)
      |> assign(:rap, false)
      |> assign(:rh, 0)
      |> assign(:sub_id, nil)
      |> assign(:index, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:subscribe_to_topic, client_id, topic, sub_opts, sub_id, index}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Prepare options for subscription
      opts = [sub_opts: sub_opts]

      # Add subscription identifier if provided
      opts = if sub_id, do: Keyword.put(opts, :id, sub_id), else: opts

      # Attempt to subscribe to the topic
      case Mqttable.MqttClient.Manager.subscribe(client_id, topic, opts) do
        {:ok, _props, _reason_codes} ->
          # Update the connection's topics list with topic and options
          # 保持 nl 和 rap 的原始值，不转换为布尔值
          nl_value = Keyword.get(sub_opts, :nl, 0)
          rap_value = Keyword.get(sub_opts, :rap, 0)

          # Get subscription identifier from the opts parameter, not from sub_opts
          sub_id_value = sub_id

          # Create topic entry with all subscription options
          topic_entry = %{
            topic: topic,
            qos: Keyword.get(sub_opts, :qos, 0),
            nl: nl_value,
            rap: rap_value,
            rh: Keyword.get(sub_opts, :rh, 0)
          }

          # Add subscription identifier if provided
          topic_entry =
            if sub_id_value && is_integer(sub_id_value) && sub_id_value > 0 do
              Map.put(topic_entry, :id, sub_id_value)
            else
              topic_entry
            end

          # Get current topics (always using new format - list of maps)
          current_topics = connection.topics || []

          # Check if we're editing an existing topic or adding a new one
          # Use the index parameter to determine if this is an edit or a new subscription
          {is_edit, updated_topics} =
            if index != nil && index != "" do
              # Try to convert index to integer
              case Integer.parse(index) do
                {idx, _} when idx >= 0 and idx < length(current_topics) ->
                  # Update existing topic at the specified index
                  {true, List.replace_at(current_topics, idx, topic_entry)}

                _ ->
                  # Invalid index, add as new topic
                  {false, current_topics ++ [topic_entry]}
              end
            else
              # No index provided, add as new topic
              {false, current_topics ++ [topic_entry]}
            end

          updated_connection = Map.put(connection, :topics, updated_topics)

          # Update the connection in the connection set
          updated_connections =
            List.replace_at(set.connections, connection_index, updated_connection)

          updated_set = Map.put(set, :connections, updated_connections)

          # Update the connection set in the connection sets list
          updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

          # Update the connection sets in the state
          ConnectionSets.update(updated_connection_sets)

          # Show success message
          message =
            if is_edit,
              do: "Updated subscription to topic: #{topic}",
              else: "Subscribed to topic: #{topic}"

          socket = put_flash(socket, :info, message)
          {:noreply, socket}

        {:error, _reason, error_message} ->
          # Show error message
          socket = put_flash(socket, :error, "Failed to subscribe: #{error_message}")
          {:noreply, socket}

        {:error, :not_connected} ->
          # Show error message
          socket = put_flash(socket, :error, "Client is not connected")
          {:noreply, socket}
      end
    else
      # Connection not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_topic_subscribed, client_id, topic, opts, sub_id}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Check if the topic is already in the list
      current_topics = connection.topics || []

      # Check if the topic exists in the list (always using new format - list of maps)
      topic_index =
        Enum.find_index(current_topics, fn t ->
          case t do
            %{topic: topic_str} -> topic_str == topic
            _ -> false
          end
        end)

      topic_exists = topic_index != nil

      if topic_exists do
        # Topic already exists, no need to update
        {:noreply, socket}
      else
        # Update the connection's topics list with topic and options
        # 保持 nl 和 rap 的原始值，不转换为布尔值
        nl_value = Keyword.get(opts, :nl, 0)
        rap_value = Keyword.get(opts, :rap, 0)

        # Get subscription identifier from the sub_id parameter
        sub_id_value = sub_id

        # Create topic entry with all subscription options
        topic_entry = %{
          topic: topic,
          qos: Keyword.get(opts, :qos, 0),
          nl: nl_value,
          rap: rap_value,
          rh: Keyword.get(opts, :rh, 0)
        }

        # Add subscription identifier if provided
        topic_entry =
          if sub_id_value && is_integer(sub_id_value) && sub_id_value > 0 do
            Map.put(topic_entry, :id, sub_id_value)
          else
            topic_entry
          end

        # Get current topics (always using new format - list of maps)
        current_topics = connection.topics || []

        # Add new topic
        updated_topics = current_topics ++ [topic_entry]

        updated_connection = Map.put(connection, :topics, updated_topics)

        # Update the connection in the connection set
        updated_connections =
          List.replace_at(set.connections, connection_index, updated_connection)

        updated_set = Map.put(set, :connections, updated_connections)

        # Update the connection set in the connection sets list
        updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

        # Update the connection sets in the state
        ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        {:noreply, socket}
      end
    else
      # Connection not found
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_topic_unsubscribed, client_id, topic}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Update the connection's topics list
      current_topics = connection.topics || []

      # Remove the topic from the list (always using new format - list of maps)
      updated_topics =
        Enum.reject(current_topics, fn t ->
          case t do
            %{topic: topic_str} -> topic_str == topic
            _ -> false
          end
        end)

      updated_connection = Map.put(connection, :topics, updated_topics)

      # Update the connection in the connection set
      updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
      updated_set = Map.put(set, :connections, updated_connections)

      # Update the connection set in the connection sets list
      updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

      # Update the connection sets in the state
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns
      socket =
        assign(socket,
          active_connection_set: updated_set,
          connection_sets: updated_connection_sets
        )

      {:noreply, socket}
    else
      # Connection not found
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_trace_message, trace_message}, socket) do
    # Add the new trace message to the end of the list (newest at bottom)
    # The TraceComponent will handle adding it to the stream
    trace_messages = socket.assigns.trace_messages ++ [trace_message]

    # Limit the number of messages to prevent excessive memory usage
    # Keep only the most recent 1000 messages (remove from beginning if needed)
    trace_messages =
      if length(trace_messages) > 1000 do
        Enum.drop(trace_messages, length(trace_messages) - 1000)
      else
        trace_messages
      end

    # Update the socket with the new trace messages
    # The TraceComponent will detect this change and update its stream
    {:noreply, assign(socket, :trace_messages, trace_messages)}
  end

  # Helper function to extract a concise error message from complex error reasons
  defp extract_concise_error_message(reason) do
    cond do
      # Handle socket_closed_before_connack errors
      is_tuple(reason) && tuple_size(reason) >= 2 &&
          elem(reason, 0) == :socket_closed_before_connack ->
        "Connection closed by broker before completing handshake"

      # Handle tcp_closed errors
      is_tuple(reason) && tuple_size(reason) >= 2 && elem(reason, 0) == :shutdown &&
          elem(reason, 1) == :tcp_closed ->
        "Connection closed by broker before completing handshake"

      # Handle GenServer call errors with nested reasons
      is_tuple(reason) && tuple_size(reason) >= 2 && is_tuple(elem(reason, 0)) ->
        inner_reason = elem(reason, 0)

        if is_tuple(inner_reason) && tuple_size(inner_reason) >= 2 do
          case elem(inner_reason, 0) do
            :shutdown ->
              case elem(inner_reason, 1) do
                :tcp_closed -> "Connection closed by broker before completing handshake"
                other -> "Connection error: #{inspect(other)}"
              end

            other ->
              "Connection error: #{inspect(other)}"
          end
        else
          "Connection error: #{inspect(inner_reason)}"
        end

      # Default case for simple errors
      true ->
        "Connection error: #{inspect(reason)}"
    end
  end

  # Helper function to find the indices of a connection in the connection sets
  defp find_connection_indices(connection_sets, client_id) do
    # Find the set index
    set_index =
      Enum.find_index(connection_sets, fn set ->
        Enum.any?(set.connections, fn conn -> conn.client_id == client_id end)
      end)

    if set_index != nil do
      # Find the connection index within the set
      set = Enum.at(connection_sets, set_index)

      connection_index =
        Enum.find_index(set.connections, fn conn -> conn.client_id == client_id end)

      {set_index, connection_index}
    else
      {nil, nil}
    end
  end

  # Private function to handle broker tab closing
  defp handle_close_broker_tab(socket, name) do
    # Find the broker to close
    broker_to_close =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if broker_to_close do
      # Disconnect all connections in this broker before closing
      if Map.get(broker_to_close, :connections) do
        Enum.each(broker_to_close.connections, fn connection ->
          if connection.client_id do
            MqttClientManager.disconnect(connection.client_id)
          end
        end)
      end

      # Remove the broker from the list
      updated_connection_sets =
        Enum.reject(socket.assigns.connection_sets, fn set ->
          set.name == name
        end)

      # Update the connection sets
      ConnectionSets.update(updated_connection_sets)

      # If this was the active broker, select another one or clear selection
      new_active_set =
        if socket.assigns.active_connection_set &&
             socket.assigns.active_connection_set.name == name do
          # Select the first available broker, or nil if none
          List.first(updated_connection_sets)
        else
          socket.assigns.active_connection_set
        end

      # Update UI state
      ui_state = %{
        expanded_sets: socket.assigns.expanded_sets,
        active_connection_set: if(new_active_set, do: new_active_set.name, else: nil)
      }

      ConnectionSets.update_ui_state(ui_state)

      socket =
        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:active_connection_set, new_active_set)
        |> put_flash(:info, "Broker '#{name}' closed successfully")

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :error, "Broker not found")}
    end
  end
end
